import dotenv from 'dotenv';
import AnalyzeNews from '../services/AnalyzeNews';
import { NewsItem } from '../types';
import Logger from '../libs/Logger';

dotenv.config();

/**
 * Example demonstrating cryptocurrency news analysis
 */

// Sample crypto news data (Vietnamese and English)
const cryptoNewsData: NewsItem[] = [
  {
    timestamp: new Date().toISOString(),
    message: '🔴 Bitcoin tăng 5% lên $42,000 sau tin Fed có thể dừng tăng lãi suất',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    message: '🔴 Ethereum giảm 3% xuống $2,500 do áp lực bán từ các whale',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 7200000).toISOString(),
    message: '🔴 BTC dominance tăng lên 52%, altcoin season có thể kết thúc',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 10800000).toISOString(),
    message: '🔴 Binance thông báo hỗ trợ staking ETH 2.0 với lãi suất 4.5% APY',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 14400000).toISOString(),
    message: '🔴 USDT market cap vượt $83B, stablecoin tiếp tục tăng trưởng mạnh',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 18000000).toISOString(),
    message: '🔴 DeFi TVL giảm 15% trong tuần qua, thanh khoản chảy về CEX',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 21600000).toISOString(),
    message: '🔴 NFT sales volume giảm 60% so với tháng trước, thị trường đang cooling down',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 25200000).toISOString(),
    message: '🔴 Solana (SOL) pump 12% sau khi công bố partnership với Google Cloud',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 28800000).toISOString(),
    message: '🔴 Crypto fear and greed index ở mức 35 (Fear), thị trường vẫn trong tâm lý tiêu cực',
    source: 'telegram_channel'
  },
  {
    timestamp: new Date(Date.now() - 32400000).toISOString(),
    message: '🔴 Whale alert: 1,000 BTC được chuyển từ unknown wallet đến Coinbase',
    source: 'telegram_channel'
  }
];

/**
 * Demonstrate crypto keyword recognition
 */
function demonstrateCryptoKeywords() {
  console.log('₿ Cryptocurrency Keywords Recognition');
  console.log('===================================\n');

  const cryptoKeywords = {
    majorCoins: ['bitcoin', 'btc', 'ethereum', 'eth', 'binance coin', 'bnb'],
    altcoins: ['solana', 'sol', 'cardano', 'ada', 'polkadot', 'dot', 'avalanche', 'avax'],
    stablecoins: ['usdt', 'usdc', 'busd', 'dai', 'tether'],
    exchanges: ['binance', 'coinbase', 'ftx', 'kraken', 'huobi', 'okx'],
    defi: ['defi', 'uniswap', 'pancakeswap', 'compound', 'aave', 'makerdao'],
    nft: ['nft', 'opensea', 'rarible', 'foundation', 'superrare'],
    vietnamese: ['tiền điện tử', 'tiền ảo', 'tài sản số', 'lên sàn', 'xuống sàn'],
    marketTerms: ['pump', 'dump', 'whale', 'hodl', 'fomo', 'fud', 'ath', 'atl']
  };

  console.log('📊 Crypto Keywords by Category:');
  for (const [category, keywords] of Object.entries(cryptoKeywords)) {
    console.log(`\n${category.toUpperCase()}:`);
    keywords.forEach(keyword => console.log(`  - ${keyword}`));
  }
  console.log('\n');
}

/**
 * Demonstrate crypto pattern extraction
 */
function demonstrateCryptoPatterns() {
  console.log('📈 Crypto Pattern Extraction');
  console.log('============================\n');

  const patterns = {
    prices: /(btc|bitcoin|eth|ethereum)\s*\$?\d+[\d,]*\.?\d*/gi,
    percentageChanges: /(btc|eth|bitcoin|ethereum|crypto)\s*(tăng|giảm|up|down)\s*\d+\.?\d*%/gi,
    marketCap: /market cap\s*\$?\d+[\d,]*\.?\d*[bmk]?/gi,
    dominance: /(btc|bitcoin)\s*dominance\s*\d+\.?\d*%/gi,
    volume: /(volume|khối lượng)\s*\$?\d+[\d,]*\.?\d*[bmk]?/gi,
    apy: /(apy|apr)\s*\d+\.?\d*%/gi
  };

  const sampleTexts = [
    'Bitcoin tăng 5% lên $42,000',
    'BTC dominance 52%',
    'ETH staking APY 4.5%',
    'USDT market cap $83B',
    'Volume giảm 60%'
  ];

  console.log('Sample crypto texts and extracted patterns:\n');

  sampleTexts.forEach((text, index) => {
    console.log(`${index + 1}. "${text}"`);
    
    for (const [patternName, regex] of Object.entries(patterns)) {
      const matches = text.match(regex);
      if (matches) {
        console.log(`   ${patternName}: ${matches.join(', ')}`);
      }
    }
    console.log('');
  });
}

/**
 * Demonstrate crypto news analysis
 */
async function demonstrateCryptoAnalysis() {
  console.log('🔄 Crypto News Analysis Demo');
  console.log('===========================\n');

  const analyzeNews = new AnalyzeNews();

  try {
    console.log('📊 Sample crypto news data:');
    cryptoNewsData.forEach((item, index) => {
      console.log(`${index + 1}. ${item.message.substring(0, 80)}...`);
    });

    console.log('\n🔄 Processing crypto news analysis...\n');

    const result = await analyzeNews.analyzeNews(cryptoNewsData);

    console.log('✅ Crypto analysis completed successfully!');
    console.log('\n📈 Analysis Results:');
    console.log('Equities:', result.analysis.Equities);
    console.log('FX:', result.analysis.FX);
    console.log('Commodities:', result.analysis.Commodities);
    console.log('Crypto:', result.analysis.Crypto);
    console.log('Rates:', result.analysis.Rates);
    console.log('Geopolitics:', result.analysis.Geopolitics);
    console.log('Key Theme:', result.analysis.KeyTheme);
    console.log('\n📝 Summary:', result.summary);

  } catch (error) {
    console.error('❌ Crypto analysis failed:', error);
  }
}

/**
 * Show crypto optimization benefits
 */
function showCryptoOptimizationBenefits() {
  console.log('🎯 Crypto News Optimization Benefits');
  console.log('===================================\n');

  console.log('1. ₿ Comprehensive Crypto Coverage:');
  console.log('   - Major cryptocurrencies (BTC, ETH, BNB, SOL, etc.)');
  console.log('   - Altcoins and stablecoins');
  console.log('   - DeFi and NFT projects\n');

  console.log('2. 🏢 Exchange & Platform Recognition:');
  console.log('   - Major exchanges (Binance, Coinbase, FTX, etc.)');
  console.log('   - DeFi platforms (Uniswap, PancakeSwap, etc.)');
  console.log('   - NFT marketplaces (OpenSea, Rarible, etc.)\n');

  console.log('3. 📊 Market Metrics Extraction:');
  console.log('   - Price movements and percentage changes');
  console.log('   - Market cap and dominance');
  console.log('   - Volume and liquidity data');
  console.log('   - APY/APR for staking and farming\n');

  console.log('4. 🇻🇳 Vietnamese Crypto Terms:');
  console.log('   - "tiền điện tử", "tiền ảo", "tài sản số"');
  console.log('   - "lên sàn", "xuống sàn" (listing/delisting)');
  console.log('   - "pump", "dump", "hold", "all in"\n');

  console.log('5. 🎯 Smart Categorization:');
  console.log('   - Separates crypto news from traditional markets');
  console.log('   - Identifies cross-market impacts');
  console.log('   - Provides dedicated crypto analysis section');
  console.log('   - Maintains context optimization benefits\n');
}

/**
 * Main execution
 */
async function main() {
  console.log('🚀 Cryptocurrency News Analysis Demo');
  console.log('====================================\n');

  try {
    demonstrateCryptoKeywords();
    demonstrateCryptoPatterns();
    await demonstrateCryptoAnalysis();
    showCryptoOptimizationBenefits();

    console.log('✅ Crypto demo completed successfully!');
    console.log('\n💡 Usage: Send crypto news to your bot with /news 1d @crypto_channel');
  } catch (error) {
    Logger.error('Demo error:', error);
  }
}

if (require.main === module) {
  main().catch(error => {
    Logger.error('Unhandled error:', error);
    process.exit(1);
  });
}
